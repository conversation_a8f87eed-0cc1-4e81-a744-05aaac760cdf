"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/unified-commission-structure/route";
exports.ids = ["app/api/admin/unified-commission-structure/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_unified_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/unified-commission-structure/route.ts */ \"(rsc)/./src/app/api/admin/unified-commission-structure/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/unified-commission-structure/route\",\n        pathname: \"/api/admin/unified-commission-structure\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/unified-commission-structure/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\unified-commission-structure\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_unified_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/unified-commission-structure/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/unified-commission-structure/route.ts":
/*!*****************************************************************!*\
  !*** ./src/app/api/admin/unified-commission-structure/route.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Get unified commission structures (one per package)\n        const { data: structures, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").select(\"*\").eq(\"commission_type\", \"unified_structure\").order(\"package_value\", {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch commission structures: ${error.message}`);\n        }\n        // Transform the data to match the expected extended schema\n        const transformedStructures = (structures || []).map((structure)=>({\n                id: structure.id,\n                commission_type: structure.commission_type,\n                package_value: structure.package_value,\n                // Map level rates to commission types\n                direct_commission_rate: structure.level_1_rate || 0,\n                level_commission_rate: structure.level_2_rate || 0,\n                voucher_rate: structure.level_3_rate || 0,\n                festival_bonus_rate: structure.level_4_rate || 0,\n                saving_rate: structure.level_5_rate || 0,\n                gift_center_rate: structure.level_6_rate || 0,\n                entertainment_rate: structure.level_7_rate || 0,\n                medical_rate: structure.level_8_rate || 0,\n                education_rate: structure.level_9_rate || 0,\n                credit_rate: structure.level_10_rate || 0,\n                // ZM and RSM rates (calculated based on package value)\n                zm_bonus_rate: calculateZMRate(structure.package_value),\n                zm_petral_allowance_rate: 0.005,\n                zm_leasing_facility_rate: 0.01,\n                zm_phone_bill_rate: 0.001,\n                rsm_bonus_rate: calculateRSMRate(structure.package_value),\n                rsm_petral_allowance_rate: 0.005,\n                rsm_leasing_facility_rate: 0.01,\n                rsm_phone_bill_rate: 0.001,\n                // Gift system rates\n                present_user_rate: structure.present_user_rate || 0,\n                present_leader_rate: structure.present_leader_rate || 0,\n                annual_present_user_rate: structure.annual_present_user_rate || 0,\n                annual_present_leader_rate: structure.annual_present_leader_rate || 0,\n                is_active: structure.is_active,\n                created_at: structure.created_at,\n                updated_at: structure.updated_at\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedStructures\n        });\n    } catch (error) {\n        console.error(\"Error fetching unified commission structures:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch commission structures\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        if (!body.package_value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Package value is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if unified structure already exists for this package\n        const { data: existing, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").select(\"id\").eq(\"commission_type\", \"unified_structure\").eq(\"package_value\", body.package_value).single();\n        if (existing) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Commission structure already exists for this package\"\n            }, {\n                status: 400\n            });\n        }\n        // Create new unified commission structure\n        const { data: newStructure, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").insert({\n            commission_type: \"unified_structure\",\n            package_value: body.package_value,\n            level_1_rate: body.direct_commission_rate || 0.10,\n            level_2_rate: body.level_commission_rate || 0.02,\n            level_3_rate: body.voucher_rate || 0.01,\n            level_4_rate: body.festival_bonus_rate || 0.01,\n            level_5_rate: body.saving_rate || 0.01,\n            level_6_rate: body.gift_center_rate || 0.005,\n            level_7_rate: body.entertainment_rate || 0.002,\n            level_8_rate: body.medical_rate || 0.001,\n            level_9_rate: body.education_rate || 0.001,\n            level_10_rate: body.credit_rate || 0.001,\n            present_user_rate: body.present_user_rate || 0.025,\n            present_leader_rate: body.present_leader_rate || 0.01,\n            annual_present_user_rate: body.annual_present_user_rate || 0.01,\n            annual_present_leader_rate: body.annual_present_leader_rate || 0.025,\n            is_active: body.is_active !== undefined ? body.is_active : true\n        }).select().single();\n        if (error) {\n            throw new Error(`Failed to create commission structure: ${error.message}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Commission structure created successfully\",\n            data: newStructure\n        });\n    } catch (error) {\n        console.error(\"Error creating commission structure:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create commission structure\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const url = new URL(request.url);\n        const id = url.pathname.split(\"/\").pop();\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Structure ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        // Update unified commission structure\n        const { data: updatedStructure, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").update({\n            level_1_rate: body.direct_commission_rate || 0.10,\n            level_2_rate: body.level_commission_rate || 0.02,\n            level_3_rate: body.voucher_rate || 0.01,\n            level_4_rate: body.festival_bonus_rate || 0.01,\n            level_5_rate: body.saving_rate || 0.01,\n            level_6_rate: body.gift_center_rate || 0.005,\n            level_7_rate: body.entertainment_rate || 0.002,\n            level_8_rate: body.medical_rate || 0.001,\n            level_9_rate: body.education_rate || 0.001,\n            level_10_rate: body.credit_rate || 0.001,\n            present_user_rate: body.present_user_rate || 0.025,\n            present_leader_rate: body.present_leader_rate || 0.01,\n            annual_present_user_rate: body.annual_present_user_rate || 0.01,\n            annual_present_leader_rate: body.annual_present_leader_rate || 0.025,\n            is_active: body.is_active !== undefined ? body.is_active : true,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).eq(\"commission_type\", \"unified_structure\").select().single();\n        if (error) {\n            throw new Error(`Failed to update commission structure: ${error.message}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Commission structure updated successfully\",\n            data: updatedStructure\n        });\n    } catch (error) {\n        console.error(\"Error updating commission structure:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update commission structure\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper functions to calculate ZM and RSM rates based on package value\nfunction calculateZMRate(packageValue) {\n    if (packageValue >= 50000) return 0.02;\n    if (packageValue >= 10000) return 0.02;\n    if (packageValue >= 5000) return 0.02;\n    return 0.02;\n}\nfunction calculateRSMRate(packageValue) {\n    if (packageValue >= 50000) return 0.028;\n    if (packageValue >= 10000) return 0.028;\n    if (packageValue >= 5000) return 0.025;\n    return 0.025;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/unified-commission-structure/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
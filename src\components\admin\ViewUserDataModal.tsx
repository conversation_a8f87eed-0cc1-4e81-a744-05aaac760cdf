'use client'

import { useState, useEffect } from 'react'
import { X, User, Mail, Phone, MapPin, Calendar, Shield, Wallet, FileText, Award, Network, Eye, EyeOff } from 'lucide-react'
import { AdminUser } from '@/lib/services/admin'
import ReferralRankBadge from '@/components/ui/ReferralRankBadge'
import { formatCurrency } from '@/lib/utils'

interface ViewUserDataModalProps {
  isOpen: boolean
  onClose: () => void
  user: AdminUser
}

interface UserWalletData {
  balance: number
  total_deposits: number
  total_withdrawals: number
  total_commissions: number
}

interface UserKYCData {
  kyc_status: string
  kyc_submitted_at?: string
  kyc_approved_at?: string
}

interface UserInviterData {
  id: string
  full_name?: string
  email: string
  user_id: number
}

export default function ViewUserDataModal({ isOpen, onClose, user }: ViewUserDataModalProps) {
  const [walletData, setWalletData] = useState<UserWalletData | null>(null)
  const [kycData, setKycData] = useState<UserKYCData | null>(null)
  const [inviterData, setInviterData] = useState<UserInviterData | null>(null)
  const [loading, setLoading] = useState(false)
  const [showSensitiveData, setShowSensitiveData] = useState(false)

  useEffect(() => {
    if (isOpen && user) {
      fetchAdditionalData()
    }
  }, [isOpen, user])

  const fetchAdditionalData = async () => {
    setLoading(true)
    try {
      // Fetch wallet data
      const walletResponse = await fetch(`/api/admin/users/${user.id}/wallet`)
      if (walletResponse.ok) {
        const wallet = await walletResponse.json()
        setWalletData(wallet)
      }

      // Fetch KYC data
      const kycResponse = await fetch(`/api/admin/users/${user.id}/kyc`)
      if (kycResponse.ok) {
        const kyc = await kycResponse.json()
        setKycData(kyc)
      }

      // Fetch inviter data if user was referred
      if (user.referred_by_id) {
        const inviterResponse = await fetch(`/api/admin/users/${user.referred_by_id}/basic`)
        if (inviterResponse.ok) {
          const inviter = await inviterResponse.json()
          setInviterData(inviter)
        }
      }
    } catch (error) {
      console.error('Error fetching additional user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100'
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'rejected': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getUserTypeDisplay = (userType?: string) => {
    switch (userType) {
      case 'okdoi_head': return 'OKDOI Head'
      case 'zonal_manager': return 'Zonal Manager'
      case 'rsm': return 'Regional Sales Manager'
      default: return 'User'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-full overflow-hidden">
              {user.avatar_url ? (
                <img
                  src={user.avatar_url}
                  alt="Profile picture"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-primary-blue/10 flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-blue" />
                </div>
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {user.full_name || 'Unnamed User'}
              </h2>
              <p className="text-sm text-gray-500">OKDOI ID: {user.user_id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSensitiveData(!showSensitiveData)}
              className="flex items-center px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              {showSensitiveData ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
              {showSensitiveData ? 'Hide' : 'Show'} Sensitive
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <User className="h-5 w-5 mr-2 text-primary-blue" />
                  Basic Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">OKDOI ID:</span>
                    <span className="font-semibold text-primary-blue">{user.user_id}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Email:</span>
                    <span className="font-medium">{showSensitiveData ? user.email : '***@***.***'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Full Name:</span>
                    <span className="font-medium">{user.full_name || 'Not provided'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Phone:</span>
                    <span className="font-medium">
                      {showSensitiveData ? (user.phone || 'Not provided') : (user.phone ? '***-***-****' : 'Not provided')}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Location:</span>
                    <span className="font-medium">{user.location || 'Not provided'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Joined:</span>
                    <span className="font-medium">{new Date(user.created_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Verified:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.is_verified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_verified ? 'Verified' : 'Not Verified'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Account Status */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-primary-blue" />
                  Account Status
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Role:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.role === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.role || 'user'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">User Type:</span>
                    <ReferralRankBadge userType={user.user_type} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Super Admin:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.is_super_admin ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.is_super_admin ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Status:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.banned_until ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {user.banned_until ? 'Banned' : 'Active'}
                    </span>
                  </div>
                  {user.banned_until && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Banned Until:</span>
                      <span className="font-medium text-red-600">
                        {new Date(user.banned_until).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Wallet Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Wallet className="h-5 w-5 mr-2 text-primary-blue" />
                  Wallet Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Current Balance:</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(user.wallet_balance || 0)}
                    </span>
                  </div>
                  {walletData && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Total Deposits:</span>
                        <span className="font-medium">{formatCurrency(walletData.total_deposits)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Total Withdrawals:</span>
                        <span className="font-medium">{formatCurrency(walletData.total_withdrawals)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Total Commissions:</span>
                        <span className="font-medium">{formatCurrency(walletData.total_commissions)}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* KYC Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-primary-blue" />
                  KYC Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">KYC Status:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      getKYCStatusColor(user.kyc_status || 'not_submitted')
                    }`}>
                      {(user.kyc_status || 'not_submitted').replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  {kycData?.kyc_submitted_at && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Submitted:</span>
                      <span className="font-medium">
                        {new Date(kycData.kyc_submitted_at).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  {kycData?.kyc_approved_at && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Approved:</span>
                      <span className="font-medium">
                        {new Date(kycData.kyc_approved_at).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Referral Information */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Network className="h-5 w-5 mr-2 text-primary-blue" />
                  Referral Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Referral Code:</span>
                    <span className="font-medium font-mono text-primary-blue">
                      {user.referral_code || 'Not generated'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Referral Level:</span>
                    <span className="font-medium">{user.referral_level || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Direct Referrals:</span>
                    <span className="font-medium">{user.direct_referrals_count || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Downline:</span>
                    <span className="font-medium">{user.total_downline_count || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Commissions:</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(user.total_commission_earned || 0)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Referral Active:</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.is_referral_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_referral_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  {/* Inviter Information */}
                  {user.referred_by_id && (
                    <div className="pt-3 border-t border-gray-200">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Invited By</h4>
                      {inviterData ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Name:</span>
                            <span className="font-medium text-gray-900">
                              {inviterData.full_name || 'Not provided'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Email:</span>
                            <span className="font-medium text-primary-blue">
                              {inviterData.email}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">OKDOI ID:</span>
                            <span className="font-medium text-primary-blue">
                              {inviterData.user_id}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-500">
                          Loading inviter information...
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

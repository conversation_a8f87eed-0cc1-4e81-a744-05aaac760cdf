import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { ExtendedCommissionStructure } from '@/types'

export async function GET(request: NextRequest) {
  try {
    // Get unified commission structures (one per package)
    const { data: structures, error } = await supabaseAdmin
      .from('commission_structure')
      .select('*')
      .eq('commission_type', 'unified_structure')
      .order('package_value', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch commission structures: ${error.message}`)
    }

    // Transform the data to match the expected extended schema
    const transformedStructures = (structures || []).map(structure => ({
      id: structure.id,
      commission_type: structure.commission_type,
      package_value: structure.package_value,
      // Map level rates to commission types
      direct_commission_rate: structure.level_1_rate || 0,
      level_commission_rate: structure.level_2_rate || 0,
      voucher_rate: structure.level_3_rate || 0,
      festival_bonus_rate: structure.level_4_rate || 0,
      saving_rate: structure.level_5_rate || 0,
      gift_center_rate: structure.level_6_rate || 0,
      entertainment_rate: structure.level_7_rate || 0,
      medical_rate: structure.level_8_rate || 0,
      education_rate: structure.level_9_rate || 0,
      credit_rate: structure.level_10_rate || 0,
      // ZM and RSM rates (calculated based on package value)
      zm_bonus_rate: calculateZMRate(structure.package_value),
      zm_petral_allowance_rate: 0.005,
      zm_leasing_facility_rate: 0.01,
      zm_phone_bill_rate: 0.001,
      rsm_bonus_rate: calculateRSMRate(structure.package_value),
      rsm_petral_allowance_rate: 0.005,
      rsm_leasing_facility_rate: 0.01,
      rsm_phone_bill_rate: 0.001,
      // Gift system rates
      present_user_rate: structure.present_user_rate || 0,
      present_leader_rate: structure.present_leader_rate || 0,
      annual_present_user_rate: structure.annual_present_user_rate || 0,
      annual_present_leader_rate: structure.annual_present_leader_rate || 0,
      is_active: structure.is_active,
      created_at: structure.created_at,
      updated_at: structure.updated_at
    }))

    return NextResponse.json({
      success: true,
      data: transformedStructures
    })
  } catch (error) {
    console.error('Error fetching unified commission structures:', error)
    return NextResponse.json(
      { error: 'Failed to fetch commission structures' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.package_value) {
      return NextResponse.json(
        { error: 'Package value is required' },
        { status: 400 }
      )
    }

    // Check if unified structure already exists for this package
    const { data: existing, error: checkError } = await supabaseAdmin
      .from('commission_structure')
      .select('id')
      .eq('commission_type', 'unified_structure')
      .eq('package_value', body.package_value)
      .single()

    if (existing) {
      return NextResponse.json(
        { error: 'Commission structure already exists for this package' },
        { status: 400 }
      )
    }

    // Create new unified commission structure
    const { data: newStructure, error } = await supabaseAdmin
      .from('commission_structure')
      .insert({
        commission_type: 'unified_structure',
        package_value: body.package_value,
        level_1_rate: body.direct_commission_rate || 0.10,
        level_2_rate: body.level_commission_rate || 0.02,
        level_3_rate: body.voucher_rate || 0.01,
        level_4_rate: body.festival_bonus_rate || 0.01,
        level_5_rate: body.saving_rate || 0.01,
        level_6_rate: body.gift_center_rate || 0.005,
        level_7_rate: body.entertainment_rate || 0.002,
        level_8_rate: body.medical_rate || 0.001,
        level_9_rate: body.education_rate || 0.001,
        level_10_rate: body.credit_rate || 0.001,
        present_user_rate: body.present_user_rate || 0.025,
        present_leader_rate: body.present_leader_rate || 0.01,
        annual_present_user_rate: body.annual_present_user_rate || 0.01,
        annual_present_leader_rate: body.annual_present_leader_rate || 0.025,
        is_active: body.is_active !== undefined ? body.is_active : true
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create commission structure: ${error.message}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Commission structure created successfully',
      data: newStructure
    })
  } catch (error) {
    console.error('Error creating commission structure:', error)
    return NextResponse.json(
      { error: 'Failed to create commission structure' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.pathname.split('/').pop()

    if (!id) {
      return NextResponse.json(
        { error: 'Structure ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()

    // Update unified commission structure
    const { data: updatedStructure, error } = await supabaseAdmin
      .from('commission_structure')
      .update({
        level_1_rate: body.direct_commission_rate || 0.10,
        level_2_rate: body.level_commission_rate || 0.02,
        level_3_rate: body.voucher_rate || 0.01,
        level_4_rate: body.festival_bonus_rate || 0.01,
        level_5_rate: body.saving_rate || 0.01,
        level_6_rate: body.gift_center_rate || 0.005,
        level_7_rate: body.entertainment_rate || 0.002,
        level_8_rate: body.medical_rate || 0.001,
        level_9_rate: body.education_rate || 0.001,
        level_10_rate: body.credit_rate || 0.001,
        present_user_rate: body.present_user_rate || 0.025,
        present_leader_rate: body.present_leader_rate || 0.01,
        annual_present_user_rate: body.annual_present_user_rate || 0.01,
        annual_present_leader_rate: body.annual_present_leader_rate || 0.025,
        is_active: body.is_active !== undefined ? body.is_active : true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('commission_type', 'unified_structure')
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update commission structure: ${error.message}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Commission structure updated successfully',
      data: updatedStructure
    })
  } catch (error) {
    console.error('Error updating commission structure:', error)
    return NextResponse.json(
      { error: 'Failed to update commission structure' },
      { status: 500 }
    )
  }
}

// Helper functions to calculate ZM and RSM rates based on package value
function calculateZMRate(packageValue: number): number {
  if (packageValue >= 50000) return 0.02
  if (packageValue >= 10000) return 0.02
  if (packageValue >= 5000) return 0.02
  return 0.02
}

function calculateRSMRate(packageValue: number): number {
  if (packageValue >= 50000) return 0.028
  if (packageValue >= 10000) return 0.028
  if (packageValue >= 5000) return 0.025
  return 0.025
}

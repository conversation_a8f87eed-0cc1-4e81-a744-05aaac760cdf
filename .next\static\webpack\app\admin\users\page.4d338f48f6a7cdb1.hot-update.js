"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/admin/users/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin/users/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminUsers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ReferralRankBadge */ \"(app-pages-browser)/./src/components/ui/ReferralRankBadge.tsx\");\n/* harmony import */ var _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/admin */ \"(app-pages-browser)/./src/lib/services/admin.ts\");\n/* harmony import */ var _components_admin_ViewUserDataModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/ViewUserDataModal */ \"(app-pages-browser)/./src/components/admin/ViewUserDataModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Lazy load the heavy referral tree component\nconst EnhancedReferralTree = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_admin_EnhancedReferralTree_tsx-_d5ef1\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/admin/EnhancedReferralTree */ \"(app-pages-browser)/./src/components/admin/EnhancedReferralTree.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\admin\\\\users\\\\page.tsx -> \" + \"@/components/admin/EnhancedReferralTree\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined),\n    ssr: false\n});\n_c = EnhancedReferralTree;\n\n\n\nfunction UserActions(param) {\n    let { user, onRoleUpdate, onBanUser, onViewTree, onViewUserData } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setShowMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: menuRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-[9999]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onViewUserData(user);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                \"View User Data\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onViewTree(user.id);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                \"View Referral Tree\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onRoleUpdate(user.id, user.role === \"admin\" ? \"user\" : \"admin\");\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                user.role === \"admin\" ? \"Remove Admin\" : \"Make Admin\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onBanUser(user.id);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                user.banned_until ? \"Unban User\" : \"Ban User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(UserActions, \"IrpJJ9vEf04eEvLSh3rG0JvlrDI=\");\n_c1 = UserActions;\nfunction AdminUsers() {\n    _s1();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searching, setSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showReferralTree, setShowReferralTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserTree, setSelectedUserTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [treeLoading, setTreeLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserInfo, setSelectedUserInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUserDataModal, setShowUserDataModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const usersPerPage = 20;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUsers();\n    }, [\n        currentPage,\n        selectedRole\n    ]);\n    // Debounced search effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSearching(true);\n        const timeoutId = setTimeout(()=>{\n            if (currentPage === 1) {\n                fetchUsers();\n            } else {\n                setCurrentPage(1) // Reset to first page when searching\n                ;\n            }\n            setSearching(false);\n        }, 500) // 500ms debounce\n        ;\n        return ()=>{\n            clearTimeout(timeoutId);\n            setSearching(false);\n        };\n    }, [\n        searchTerm\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            setLoading(true);\n            const { users: userData, total } = await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.getAllUsers(currentPage, usersPerPage, searchTerm.trim() || undefined, selectedRole !== \"all\" ? selectedRole : undefined);\n            setUsers(userData);\n            setTotalUsers(total);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load users\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRoleUpdate = async (userId, newRole)=>{\n        try {\n            await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.updateUserRole(userId, newRole);\n            await fetchUsers() // Refresh the list\n            ;\n        } catch (err) {\n            alert(\"Failed to update user role\");\n        }\n    };\n    const handleBanUser = async (userId)=>{\n        try {\n            const user = users.find((u)=>u.id === userId);\n            const banUntil = (user === null || user === void 0 ? void 0 : user.banned_until) ? undefined : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n            await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.banUser(userId, banUntil);\n            await fetchUsers() // Refresh the list\n            ;\n        } catch (err) {\n            await showAlert({\n                title: \"Error\",\n                message: \"Failed to ban/unban user\",\n                variant: \"danger\"\n            });\n        }\n    };\n    const handleViewUserData = (user)=>{\n        setSelectedUserData(user);\n        setShowUserDataModal(true);\n    };\n    const handleViewReferralTree = async (userId)=>{\n        try {\n            const user = users.find((u)=>u.id === userId);\n            if (!user) return;\n            setSelectedUserInfo(user);\n            setShowReferralTree(true);\n            setTreeLoading(true);\n            const response = await fetch(\"/api/admin/network-tree?rootUserId=\".concat(userId, \"&maxDepth=10&lazy=true\"));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to load referral tree\");\n            }\n            setSelectedUserTree(result.data);\n        } catch (error) {\n            console.error(\"Error loading referral tree:\", error);\n            alert(\"Failed to load referral tree\");\n        } finally{\n            setTreeLoading(false);\n        }\n    };\n    // Users are already filtered server-side, no need for client-side filtering\n    const filteredUsers = users;\n    const totalPages = Math.ceil(totalUsers / usersPerPage);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-300 rounded w-1/4 mb-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: Array.from({\n                                    length: 10\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 bg-gray-300 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-300 rounded w-1/4 mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gray-300 rounded w-1/3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Users Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Manage user accounts and permissions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        totalUsers,\n                                        \" total users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search users by name or email...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this),\n                                        searching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedRole,\n                                    onChange: (e)=>setSelectedRole(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Roles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"user\",\n                                            children: \"Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"admin\",\n                                            children: \"Admins\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Role\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Referral Rank\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Wallet Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Joined\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"divide-y divide-gray-200\",\n                                    children: filteredUsers.map((user)=>{\n                                        var _user_full_name, _user_email;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-10 w-10 rounded-full overflow-hidden\",\n                                                                        children: user.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: user.avatar_url,\n                                                                            alt: \"Profile picture\",\n                                                                            className: \"w-full h-full object-cover object-center\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-primary-blue\",\n                                                                                children: ((_user_full_name = user.full_name) === null || _user_full_name === void 0 ? void 0 : _user_full_name.charAt(0)) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.charAt(0)) || \"U\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-1 -right-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            userType: user.user_type,\n                                                                            size: \"sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: user.full_name || \"No name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            user.email\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            user.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.role === \"admin\" || user.is_super_admin ? \"bg-purple-100 text-purple-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: user.is_super_admin ? \"Super Admin\" : user.role || \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        userType: user.user_type,\n                                                        showLabel: true,\n                                                        size: \"sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.banned_until ? \"bg-red-100 text-red-800\" : \"bg-green-100 text-green-800\"),\n                                                        children: user.banned_until ? \"Banned\" : \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Rs \",\n                                                                (user.wallet_balance || 0).toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            new Date(user.created_at).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: !user.is_super_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserActions, {\n                                                        user: user,\n                                                        onRoleUpdate: handleRoleUpdate,\n                                                        onBanUser: handleBanUser,\n                                                        onViewTree: handleViewReferralTree,\n                                                        onViewUserData: handleViewUserData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this),\n                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * usersPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * usersPerPage, totalUsers),\n                                \" of \",\n                                totalUsers,\n                                \" users\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this),\n                showReferralTree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Referral Tree\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedUserInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            selectedUserInfo.full_name || \"N/A\",\n                                                            \" (\",\n                                                            selectedUserInfo.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowReferralTree(false);\n                                            setSelectedUserTree(null);\n                                            setSelectedUserInfo(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedReferralTree, {\n                                    rootNode: selectedUserTree,\n                                    loading: treeLoading,\n                                    onNodeClick: (user)=>{\n                                        console.log(\"Node clicked:\", user);\n                                    // You can add more functionality here like showing user details\n                                    },\n                                    className: \"h-full\",\n                                    onRefresh: ()=>handleViewReferralTree((selectedUserInfo === null || selectedUserInfo === void 0 ? void 0 : selectedUserInfo.id) || \"\"),\n                                    isRefreshing: treeLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Click on nodes to view details • Expand/collapse using the arrow buttons\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowReferralTree(false);\n                                                setSelectedUserTree(null);\n                                                setSelectedUserInfo(null);\n                                            },\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, this),\n                showUserDataModal && selectedUserData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ViewUserDataModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: showUserDataModal,\n                    onClose: ()=>{\n                        setShowUserDataModal(false);\n                        setSelectedUserData(null);\n                    },\n                    user: selectedUserData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminUsers, \"5VKG845ipRRJYRD56seRLcYhKFM=\");\n_c2 = AdminUsers;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedReferralTree\");\n$RefreshReg$(_c1, \"UserActions\");\n$RefreshReg$(_c2, \"AdminUsers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vdXNlcnMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQWU5QjtBQUNtQztBQUV0QjtBQUdsQyw4Q0FBOEM7QUFDOUMsTUFBTWdCLHVCQUF1QkQsd0RBQU9BLENBQ2xDLElBQU0sMFJBQU87Ozs7OztJQUVYRSxTQUFTLGtCQUNQLDhEQUFDQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHbkJDLEtBQUs7O0tBUkhKO0FBVzJEO0FBQ0g7QUFDTTtBQVVwRSxTQUFTUSxZQUFZLEtBQStFO1FBQS9FLEVBQUVDLElBQUksRUFBRUMsWUFBWSxFQUFFQyxTQUFTLEVBQUVDLFVBQVUsRUFBRUMsY0FBYyxFQUFvQixHQUEvRTs7SUFDbkIsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNZ0MsVUFBVTlCLDZDQUFNQSxDQUFpQjtJQUV2QyxtQ0FBbUM7SUFDbkNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWdDLHFCQUFxQixDQUFDQztZQUMxQixJQUFJRixRQUFRRyxPQUFPLElBQUksQ0FBQ0gsUUFBUUcsT0FBTyxDQUFDQyxRQUFRLENBQUNGLE1BQU1HLE1BQU0sR0FBVztnQkFDdEVOLFlBQVk7WUFDZDtRQUNGO1FBRUFPLFNBQVNDLGdCQUFnQixDQUFDLGFBQWFOO1FBQ3ZDLE9BQU8sSUFBTUssU0FBU0UsbUJBQW1CLENBQUMsYUFBYVA7SUFDekQsR0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNmO1FBQUlDLFdBQVU7UUFBV3NCLEtBQUtUOzswQkFDN0IsOERBQUNVO2dCQUNDQyxTQUFTLElBQU1aLFlBQVksQ0FBQ0Q7Z0JBQzVCWCxXQUFVOzBCQUVWLDRFQUFDZCxtSkFBWUE7b0JBQUNjLFdBQVU7Ozs7Ozs7Ozs7O1lBR3pCVywwQkFDQyw4REFBQ1o7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3VCOzRCQUNDQyxTQUFTO2dDQUNQZCxlQUFlSjtnQ0FDZk0sWUFBWTs0QkFDZDs0QkFDQVosV0FBVTs7OENBRVYsOERBQUNQLG1KQUFJQTtvQ0FBQ08sV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7OztzQ0FHbkMsOERBQUN1Qjs0QkFDQ0MsU0FBUztnQ0FDUGYsV0FBV0gsS0FBS21CLEVBQUU7Z0NBQ2xCYixZQUFZOzRCQUNkOzRCQUNBWixXQUFVOzs4Q0FFViw4REFBQ1IsbUpBQU9BO29DQUFDUSxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7O3NDQUd0Qyw4REFBQ3VCOzRCQUNDQyxTQUFTO2dDQUNQakIsYUFBYUQsS0FBS21CLEVBQUUsRUFBRW5CLEtBQUtvQixJQUFJLEtBQUssVUFBVSxTQUFTO2dDQUN2RGQsWUFBWTs0QkFDZDs0QkFDQVosV0FBVTs7OENBRVYsOERBQUNiLG9KQUFNQTtvQ0FBQ2EsV0FBVTs7Ozs7O2dDQUNqQk0sS0FBS29CLElBQUksS0FBSyxVQUFVLGlCQUFpQjs7Ozs7OztzQ0FFNUMsOERBQUNIOzRCQUNDQyxTQUFTO2dDQUNQaEIsVUFBVUYsS0FBS21CLEVBQUU7Z0NBQ2pCYixZQUFZOzRCQUNkOzRCQUNBWixXQUFVOzs4Q0FFViw4REFBQ1osb0pBQUdBO29DQUFDWSxXQUFVOzs7Ozs7Z0NBQ2RNLEtBQUtxQixZQUFZLEdBQUcsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xEO0dBekVTdEI7TUFBQUE7QUEyRU0sU0FBU3VCOztJQUN0QixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2pELCtDQUFRQSxDQUFjLEVBQUU7SUFDbEQsTUFBTSxDQUFDaUIsU0FBU2lDLFdBQVcsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21ELFdBQVdDLGFBQWEsR0FBR3BELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3FELE9BQU9DLFNBQVMsR0FBR3RELCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN5RCxhQUFhQyxlQUFlLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyRCxZQUFZQyxjQUFjLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2RCxjQUFjQyxnQkFBZ0IsR0FBRzlELCtDQUFRQSxDQUFTO0lBQ3pELE1BQU0sQ0FBQytELGtCQUFrQkMsb0JBQW9CLEdBQUdoRSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNpRSxrQkFBa0JDLG9CQUFvQixHQUFHbEUsK0NBQVFBLENBQWlDO0lBQ3pGLE1BQU0sQ0FBQ21FLGFBQWFDLGVBQWUsR0FBR3BFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3FFLGtCQUFrQkMsb0JBQW9CLEdBQUd0RSwrQ0FBUUEsQ0FBbUI7SUFDM0UsTUFBTSxDQUFDdUUsbUJBQW1CQyxxQkFBcUIsR0FBR3hFLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3lFLGtCQUFrQkMsb0JBQW9CLEdBQUcxRSwrQ0FBUUEsQ0FBbUI7SUFFM0UsTUFBTTJFLGVBQWU7SUFFckIxRSxnREFBU0EsQ0FBQztRQUNSMkU7SUFDRixHQUFHO1FBQUNuQjtRQUFhSTtLQUFhO0lBRTlCLDBCQUEwQjtJQUMxQjVELGdEQUFTQSxDQUFDO1FBQ1JtRCxhQUFhO1FBQ2IsTUFBTXlCLFlBQVlDLFdBQVc7WUFDM0IsSUFBSXJCLGdCQUFnQixHQUFHO2dCQUNyQm1CO1lBQ0YsT0FBTztnQkFDTGxCLGVBQWUsR0FBRyxxQ0FBcUM7O1lBQ3pEO1lBQ0FOLGFBQWE7UUFDZixHQUFHLEtBQUssaUJBQWlCOztRQUV6QixPQUFPO1lBQ0wyQixhQUFhRjtZQUNiekIsYUFBYTtRQUNmO0lBQ0YsR0FBRztRQUFDRztLQUFXO0lBRWYsTUFBTXFCLGFBQWE7UUFDakIsSUFBSTtZQUNGMUIsV0FBVztZQUNYLE1BQU0sRUFBRUYsT0FBT2dDLFFBQVEsRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTTNELDZEQUFZQSxDQUFDNEQsV0FBVyxDQUMvRHpCLGFBQ0FrQixjQUNBcEIsV0FBVzRCLElBQUksTUFBTUMsV0FDckJ2QixpQkFBaUIsUUFBUUEsZUFBZXVCO1lBRTFDbkMsU0FBUytCO1lBQ1RwQixjQUFjcUI7UUFDaEIsRUFBRSxPQUFPSSxLQUFLO1lBQ1ovQixTQUFTK0IsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUnJDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXNDLG1CQUFtQixPQUFPQyxRQUFnQkM7UUFDOUMsSUFBSTtZQUNGLE1BQU1wRSw2REFBWUEsQ0FBQ3FFLGNBQWMsQ0FBQ0YsUUFBUUM7WUFDMUMsTUFBTWQsYUFBYSxtQkFBbUI7O1FBQ3hDLEVBQUUsT0FBT1MsS0FBSztZQUNaTyxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixPQUFPSjtRQUMzQixJQUFJO1lBQ0YsTUFBTWhFLE9BQU91QixNQUFNOEMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbkQsRUFBRSxLQUFLNkM7WUFDdEMsTUFBTU8sV0FBV3ZFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXFCLFlBQVksSUFBR3NDLFlBQVksSUFBSWEsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUs7WUFDNUYsTUFBTTVFLDZEQUFZQSxDQUFDNkUsT0FBTyxDQUFDVixRQUFRTztZQUNuQyxNQUFNcEIsYUFBYSxtQkFBbUI7O1FBQ3hDLEVBQUUsT0FBT1MsS0FBSztZQUNaLE1BQU1lLFVBQVU7Z0JBQ2RDLE9BQU87Z0JBQ1BkLFNBQVM7Z0JBQ1RlLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQzlFO1FBQzFCaUQsb0JBQW9CakQ7UUFDcEIrQyxxQkFBcUI7SUFDdkI7SUFFQSxNQUFNZ0MseUJBQXlCLE9BQU9mO1FBQ3BDLElBQUk7WUFDRixNQUFNaEUsT0FBT3VCLE1BQU04QyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVuRCxFQUFFLEtBQUs2QztZQUN0QyxJQUFJLENBQUNoRSxNQUFNO1lBRVg2QyxvQkFBb0I3QztZQUNwQnVDLG9CQUFvQjtZQUNwQkksZUFBZTtZQUVmLE1BQU1xQyxXQUFXLE1BQU1DLE1BQU0sc0NBQTZDLE9BQVBqQixRQUFPO1lBQzFFLE1BQU1rQixTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFFbEMsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSXZCLE1BQU1xQixPQUFPdEQsS0FBSyxJQUFJO1lBQ2xDO1lBRUFhLG9CQUFvQnlDLE9BQU9HLElBQUk7UUFDakMsRUFBRSxPQUFPekQsT0FBTztZQUNkMEQsUUFBUTFELEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDdUMsTUFBTTtRQUNSLFNBQVU7WUFDUnhCLGVBQWU7UUFDakI7SUFDRjtJQUVBLDRFQUE0RTtJQUM1RSxNQUFNNEMsZ0JBQWdCaEU7SUFFdEIsTUFBTWlFLGFBQWFDLEtBQUtDLElBQUksQ0FBQ3hELGFBQWFnQjtJQUUxQyxJQUFJMUQsU0FBUztRQUNYLHFCQUNFLDhEQUFDSCxxRUFBV0E7c0JBQ1YsNEVBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzBDQUVqQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1ppRyxNQUFNQyxJQUFJLENBQUM7b0NBQUVDLFFBQVE7Z0NBQUcsR0FBR0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUNsQyw4REFBQ3ZHO3dDQUFZQyxXQUFVO2tEQUNyQiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNEOzREQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBTFhzRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBZXhCO0lBRUEscUJBQ0UsOERBQUMzRyxxRUFBV0E7a0JBQ1YsNEVBQUNJO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ3dHO29DQUFHdkcsV0FBVTs4Q0FBbUM7Ozs7Ozs4Q0FDakQsOERBQUN3RztvQ0FBRXhHLFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7c0NBRS9CLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNoQixvSkFBS0E7b0NBQUNnQixXQUFVOzs7Ozs7OENBQ2pCLDhEQUFDeUc7O3dDQUFNakU7d0NBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS3RCLDhEQUFDekM7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2Ysb0pBQU1BOzRDQUFDZSxXQUFVOzs7Ozs7c0RBQ2xCLDhEQUFDMEc7NENBQ0NDLE1BQUs7NENBQ0xDLGFBQVk7NENBQ1pDLE9BQU96RTs0Q0FDUDBFLFVBQVUsQ0FBQ0MsSUFBTTFFLGNBQWMwRSxFQUFFN0YsTUFBTSxDQUFDMkYsS0FBSzs0Q0FDN0M3RyxXQUFVOzs7Ozs7d0NBRVhnQywyQkFDQyw4REFBQ2pDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLdkIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDZ0g7b0NBQ0NILE9BQU9uRTtvQ0FDUG9FLFVBQVUsQ0FBQ0MsSUFBTXBFLGdCQUFnQm9FLEVBQUU3RixNQUFNLENBQUMyRixLQUFLO29DQUMvQzdHLFdBQVU7O3NEQUVWLDhEQUFDaUg7NENBQU9KLE9BQU07c0RBQU07Ozs7OztzREFDcEIsOERBQUNJOzRDQUFPSixPQUFNO3NEQUFPOzs7Ozs7c0RBQ3JCLDhEQUFDSTs0Q0FBT0osT0FBTTtzREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPOUIsOERBQUM5RztvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNrSDs0QkFBTWxILFdBQVU7OzhDQUNmLDhEQUFDbUg7b0NBQU1uSCxXQUFVOzhDQUNmLDRFQUFDb0g7OzBEQUNDLDhEQUFDQztnREFBR3JILFdBQVU7MERBQWlGOzs7Ozs7MERBRy9GLDhEQUFDcUg7Z0RBQUdySCxXQUFVOzBEQUFpRjs7Ozs7OzBEQUcvRiw4REFBQ3FIO2dEQUFHckgsV0FBVTswREFBaUY7Ozs7OzswREFHL0YsOERBQUNxSDtnREFBR3JILFdBQVU7MERBQWlGOzs7Ozs7MERBRy9GLDhEQUFDcUg7Z0RBQUdySCxXQUFVOzBEQUFpRjs7Ozs7OzBEQUcvRiw4REFBQ3FIO2dEQUFHckgsV0FBVTswREFBaUY7Ozs7OzswREFHL0YsOERBQUNxSDtnREFBR3JILFdBQVU7MERBQWlGOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLbkcsOERBQUNzSDtvQ0FBTXRILFdBQVU7OENBQ2Q2RixjQUFjTyxHQUFHLENBQUMsQ0FBQzlGOzRDQWVEQSxpQkFBNkJBOzZEQWQ5Qyw4REFBQzhHOzRDQUFpQnBILFdBQVU7OzhEQUMxQiw4REFBQ3VIO29EQUFHdkgsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNaTSxLQUFLa0gsVUFBVSxpQkFDZCw4REFBQ0M7NEVBQ0NDLEtBQUtwSCxLQUFLa0gsVUFBVTs0RUFDcEJHLEtBQUk7NEVBQ0ozSCxXQUFVOzs7OztpR0FHWiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUN5RztnRkFBS3pHLFdBQVU7MEZBQ2JNLEVBQUFBLGtCQUFBQSxLQUFLc0gsU0FBUyxjQUFkdEgsc0NBQUFBLGdCQUFnQnVILE1BQU0sQ0FBQyxTQUFNdkgsY0FBQUEsS0FBS3dILEtBQUssY0FBVnhILGtDQUFBQSxZQUFZdUgsTUFBTSxDQUFDLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBTS9ELDhEQUFDOUg7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNFLHdFQUFpQkE7NEVBQ2hCNkgsVUFBVXpILEtBQUswSCxTQUFTOzRFQUN4QkMsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBSVgsOERBQUNsSTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNaTSxLQUFLc0gsU0FBUyxJQUFJOzs7Ozs7a0ZBRXJCLDhEQUFDN0g7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDVixvSkFBSUE7Z0ZBQUNVLFdBQVU7Ozs7Ozs0RUFDZk0sS0FBS3dILEtBQUs7Ozs7Ozs7b0VBRVp4SCxLQUFLNEgsS0FBSyxrQkFDVCw4REFBQ25JO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ1Qsb0pBQUtBO2dGQUFDUyxXQUFVOzs7Ozs7NEVBQ2hCTSxLQUFLNEgsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1yQiw4REFBQ1g7b0RBQUd2SCxXQUFVOzhEQUNaLDRFQUFDeUc7d0RBQUt6RyxXQUFXLDJFQUloQixPQUhDTSxLQUFLb0IsSUFBSSxLQUFLLFdBQVdwQixLQUFLNkgsY0FBYyxHQUN4QyxrQ0FDQTtrRUFFSDdILEtBQUs2SCxjQUFjLEdBQUcsZ0JBQWdCN0gsS0FBS29CLElBQUksSUFBSTs7Ozs7Ozs7Ozs7OERBR3hELDhEQUFDNkY7b0RBQUd2SCxXQUFVOzhEQUNaLDRFQUFDRSx3RUFBaUJBO3dEQUNoQjZILFVBQVV6SCxLQUFLMEgsU0FBUzt3REFDeEJJLFdBQVc7d0RBQ1hILE1BQUs7Ozs7Ozs7Ozs7OzhEQUdULDhEQUFDVjtvREFBR3ZILFdBQVU7OERBQ1osNEVBQUN5Rzt3REFBS3pHLFdBQVcsMkVBSWhCLE9BSENNLEtBQUtxQixZQUFZLEdBQ2IsNEJBQ0E7a0VBRUhyQixLQUFLcUIsWUFBWSxHQUFHLFdBQVc7Ozs7Ozs7Ozs7OzhEQUdwQyw4REFBQzRGO29EQUFHdkgsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUN5Rzs0REFBS3pHLFdBQVU7O2dFQUFjO2dFQUN2Qk0sQ0FBQUEsS0FBSytILGNBQWMsSUFBSSxHQUFHQyxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJbkQsOERBQUNmO29EQUFHdkgsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDWCxvSkFBUUE7Z0VBQUNXLFdBQVU7Ozs7Ozs0REFDbkIsSUFBSThFLEtBQUt4RSxLQUFLaUksVUFBVSxFQUFFQyxrQkFBa0I7Ozs7Ozs7Ozs7Ozs4REFHakQsOERBQUNqQjtvREFBR3ZILFdBQVU7OERBQ1gsQ0FBQ00sS0FBSzZILGNBQWMsa0JBQ25CLDhEQUFDOUg7d0RBQ0NDLE1BQU1BO3dEQUNOQyxjQUFjOEQ7d0RBQ2Q3RCxXQUFXa0U7d0RBQ1hqRSxZQUFZNEU7d0RBQ1ozRSxnQkFBZ0IwRTs7Ozs7Ozs7Ozs7OzJDQXpGZjlFLEtBQUttQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBcUd6QnFFLGFBQWEsbUJBQ1osOERBQUMvRjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOztnQ0FBd0I7Z0NBQzFCc0MsQ0FBQUEsY0FBYyxLQUFLa0IsZUFBZ0I7Z0NBQUU7Z0NBQUt1QyxLQUFLMEMsR0FBRyxDQUFDbkcsY0FBY2tCLGNBQWNoQjtnQ0FBWTtnQ0FBS0E7Z0NBQVc7Ozs7Ozs7c0NBRXhILDhEQUFDekM7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdUI7b0NBQ0NDLFNBQVMsSUFBTWUsZUFBZW1HLENBQUFBLE9BQVEzQyxLQUFLNEMsR0FBRyxDQUFDRCxPQUFPLEdBQUc7b0NBQ3pERSxVQUFVdEcsZ0JBQWdCO29DQUMxQnRDLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ3VCO29DQUNDQyxTQUFTLElBQU1lLGVBQWVtRyxDQUFBQSxPQUFRM0MsS0FBSzBDLEdBQUcsQ0FBQ0MsT0FBTyxHQUFHNUM7b0NBQ3pEOEMsVUFBVXRHLGdCQUFnQndEO29DQUMxQjlGLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFRTjRDLGtDQUNDLDhEQUFDN0M7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNSLG1KQUFPQTtnREFBQ1EsV0FBVTs7Ozs7OzBEQUNuQiw4REFBQ0Q7O2tFQUNDLDhEQUFDOEk7d0RBQUc3SSxXQUFVO2tFQUFzQzs7Ozs7O29EQUNuRGtELGtDQUNDLDhEQUFDc0Q7d0RBQUV4RyxXQUFVOzs0REFDVmtELGlCQUFpQjBFLFNBQVMsSUFBSTs0REFBTTs0REFBRzFFLGlCQUFpQjRFLEtBQUs7NERBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3ZFLDhEQUFDdkc7d0NBQ0NDLFNBQVM7NENBQ1BxQixvQkFBb0I7NENBQ3BCRSxvQkFBb0I7NENBQ3BCSSxvQkFBb0I7d0NBQ3RCO3dDQUNBbkQsV0FBVTtrREFFViw0RUFBQ04sb0pBQUNBOzRDQUFDTSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJakIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDSDtvQ0FDQ2lKLFVBQVVoRztvQ0FDVmhELFNBQVNrRDtvQ0FDVCtGLGFBQWEsQ0FBQ3pJO3dDQUNac0YsUUFBUW9ELEdBQUcsQ0FBQyxpQkFBaUIxSTtvQ0FDN0IsZ0VBQWdFO29DQUNsRTtvQ0FDQU4sV0FBVTtvQ0FDVmlKLFdBQVcsSUFBTTVELHVCQUF1Qm5DLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCekIsRUFBRSxLQUFJO29DQUNoRXlILGNBQWNsRzs7Ozs7Ozs7Ozs7MENBSWxCLDhEQUFDakQ7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7c0RBR3ZDLDhEQUFDdUI7NENBQ0NDLFNBQVM7Z0RBQ1BxQixvQkFBb0I7Z0RBQ3BCRSxvQkFBb0I7Z0RBQ3BCSSxvQkFBb0I7NENBQ3RCOzRDQUNBbkQsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFVVm9ELHFCQUFxQkUsa0NBQ3BCLDhEQUFDbEQsMkVBQWlCQTtvQkFDaEIrSSxRQUFRL0Y7b0JBQ1JnRyxTQUFTO3dCQUNQL0YscUJBQXFCO3dCQUNyQkUsb0JBQW9CO29CQUN0QjtvQkFDQWpELE1BQU1nRDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNbEI7SUEzYXdCMUI7TUFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9hZG1pbi91c2Vycy9wYWdlLnRzeD9hYmUwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7XG4gIFVzZXJzLFxuICBTZWFyY2gsXG4gIEZpbHRlcixcbiAgTW9yZVZlcnRpY2FsLFxuICBTaGllbGQsXG4gIEJhbixcbiAgVXNlckNoZWNrLFxuICBDYWxlbmRhcixcbiAgTWFpbCxcbiAgUGhvbmUsXG4gIE5ldHdvcmssXG4gIFVzZXIsXG4gIFhcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IEFkbWluTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9hZG1pbi9BZG1pbkxheW91dCdcbmltcG9ydCBIaWVyYXJjaGljYWxOZXR3b3JrVHJlZSwgeyBOZXR3b3JrVHJlZU5vZGUgfSBmcm9tICdAL2NvbXBvbmVudHMvYWRtaW4vSGllcmFyY2hpY2FsTmV0d29ya1RyZWUnXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnXG5pbXBvcnQgeyBFbmhhbmNlZE5ldHdvcmtUcmVlTm9kZSB9IGZyb20gJ0AvY29tcG9uZW50cy9hZG1pbi9FbmhhbmNlZFJlZmVycmFsVHJlZSdcblxuLy8gTGF6eSBsb2FkIHRoZSBoZWF2eSByZWZlcnJhbCB0cmVlIGNvbXBvbmVudFxuY29uc3QgRW5oYW5jZWRSZWZlcnJhbFRyZWUgPSBkeW5hbWljKFxuICAoKSA9PiBpbXBvcnQoJ0AvY29tcG9uZW50cy9hZG1pbi9FbmhhbmNlZFJlZmVycmFsVHJlZScpLFxuICB7XG4gICAgbG9hZGluZzogKCkgPT4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTk2XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeS1ibHVlXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApLFxuICAgIHNzcjogZmFsc2VcbiAgfVxuKVxuaW1wb3J0IFJlZmVycmFsUmFua0JhZGdlIGZyb20gJ0AvY29tcG9uZW50cy91aS9SZWZlcnJhbFJhbmtCYWRnZSdcbmltcG9ydCB7IEFkbWluU2VydmljZSwgQWRtaW5Vc2VyIH0gZnJvbSAnQC9saWIvc2VydmljZXMvYWRtaW4nXG5pbXBvcnQgVmlld1VzZXJEYXRhTW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL2FkbWluL1ZpZXdVc2VyRGF0YU1vZGFsJ1xuXG5pbnRlcmZhY2UgVXNlckFjdGlvbnNQcm9wcyB7XG4gIHVzZXI6IEFkbWluVXNlclxuICBvblJvbGVVcGRhdGU6ICh1c2VySWQ6IHN0cmluZywgcm9sZTogc3RyaW5nKSA9PiB2b2lkXG4gIG9uQmFuVXNlcjogKHVzZXJJZDogc3RyaW5nKSA9PiB2b2lkXG4gIG9uVmlld1RyZWU6ICh1c2VySWQ6IHN0cmluZykgPT4gdm9pZFxuICBvblZpZXdVc2VyRGF0YTogKHVzZXI6IEFkbWluVXNlcikgPT4gdm9pZFxufVxuXG5mdW5jdGlvbiBVc2VyQWN0aW9ucyh7IHVzZXIsIG9uUm9sZVVwZGF0ZSwgb25CYW5Vc2VyLCBvblZpZXdUcmVlLCBvblZpZXdVc2VyRGF0YSB9OiBVc2VyQWN0aW9uc1Byb3BzKSB7XG4gIGNvbnN0IFtzaG93TWVudSwgc2V0U2hvd01lbnVdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IG1lbnVSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG5cbiAgLy8gQ2xvc2UgbWVudSB3aGVuIGNsaWNraW5nIG91dHNpZGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAgIGlmIChtZW51UmVmLmN1cnJlbnQgJiYgIW1lbnVSZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQgYXMgTm9kZSkpIHtcbiAgICAgICAgc2V0U2hvd01lbnUoZmFsc2UpXG4gICAgICB9XG4gICAgfVxuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKVxuICAgIHJldHVybiAoKSA9PiBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpXG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiIHJlZj17bWVudVJlZn0+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dNZW51KCFzaG93TWVudSl9XG4gICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICA+XG4gICAgICAgIDxNb3JlVmVydGljYWwgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICA8L2J1dHRvbj5cbiAgICAgIFxuICAgICAge3Nob3dNZW51ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0wIG10LTIgdy00OCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHotWzk5OTldXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICBvblZpZXdVc2VyRGF0YSh1c2VyKVxuICAgICAgICAgICAgICAgIHNldFNob3dNZW51KGZhbHNlKVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFZpZXcgVXNlciBEYXRhXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIG9uVmlld1RyZWUodXNlci5pZClcbiAgICAgICAgICAgICAgICBzZXRTaG93TWVudShmYWxzZSlcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdy1mdWxsIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TmV0d29yayBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBWaWV3IFJlZmVycmFsIFRyZWVcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgb25Sb2xlVXBkYXRlKHVzZXIuaWQsIHVzZXIucm9sZSA9PT0gJ2FkbWluJyA/ICd1c2VyJyA6ICdhZG1pbicpXG4gICAgICAgICAgICAgICAgc2V0U2hvd01lbnUoZmFsc2UpXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHctZnVsbCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICB7dXNlci5yb2xlID09PSAnYWRtaW4nID8gJ1JlbW92ZSBBZG1pbicgOiAnTWFrZSBBZG1pbid9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIG9uQmFuVXNlcih1c2VyLmlkKVxuICAgICAgICAgICAgICAgIHNldFNob3dNZW51KGZhbHNlKVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB3LWZ1bGwgcHgtNCBweS0yIHRleHQtc20gdGV4dC1yZWQtNjAwIGhvdmVyOmJnLXJlZC01MFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxCYW4gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAge3VzZXIuYmFubmVkX3VudGlsID8gJ1VuYmFuIFVzZXInIDogJ0JhbiBVc2VyJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWRtaW5Vc2VycygpIHtcbiAgY29uc3QgW3VzZXJzLCBzZXRVc2Vyc10gPSB1c2VTdGF0ZTxBZG1pblVzZXJbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzZWFyY2hpbmcsIHNldFNlYXJjaGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2N1cnJlbnRQYWdlLCBzZXRDdXJyZW50UGFnZV0gPSB1c2VTdGF0ZSgxKVxuICBjb25zdCBbdG90YWxVc2Vycywgc2V0VG90YWxVc2Vyc10gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbc2VsZWN0ZWRSb2xlLCBzZXRTZWxlY3RlZFJvbGVdID0gdXNlU3RhdGU8c3RyaW5nPignYWxsJylcbiAgY29uc3QgW3Nob3dSZWZlcnJhbFRyZWUsIHNldFNob3dSZWZlcnJhbFRyZWVdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZFVzZXJUcmVlLCBzZXRTZWxlY3RlZFVzZXJUcmVlXSA9IHVzZVN0YXRlPEVuaGFuY2VkTmV0d29ya1RyZWVOb2RlIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3RyZWVMb2FkaW5nLCBzZXRUcmVlTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkVXNlckluZm8sIHNldFNlbGVjdGVkVXNlckluZm9dID0gdXNlU3RhdGU8QWRtaW5Vc2VyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3Nob3dVc2VyRGF0YU1vZGFsLCBzZXRTaG93VXNlckRhdGFNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkVXNlckRhdGEsIHNldFNlbGVjdGVkVXNlckRhdGFdID0gdXNlU3RhdGU8QWRtaW5Vc2VyIHwgbnVsbD4obnVsbClcblxuICBjb25zdCB1c2Vyc1BlclBhZ2UgPSAyMFxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hVc2VycygpXG4gIH0sIFtjdXJyZW50UGFnZSwgc2VsZWN0ZWRSb2xlXSlcblxuICAvLyBEZWJvdW5jZWQgc2VhcmNoIGVmZmVjdFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFNlYXJjaGluZyh0cnVlKVxuICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKGN1cnJlbnRQYWdlID09PSAxKSB7XG4gICAgICAgIGZldGNoVXNlcnMoKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0Q3VycmVudFBhZ2UoMSkgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIHNlYXJjaGluZ1xuICAgICAgfVxuICAgICAgc2V0U2VhcmNoaW5nKGZhbHNlKVxuICAgIH0sIDUwMCkgLy8gNTAwbXMgZGVib3VuY2VcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuICAgICAgc2V0U2VhcmNoaW5nKGZhbHNlKVxuICAgIH1cbiAgfSwgW3NlYXJjaFRlcm1dKVxuXG4gIGNvbnN0IGZldGNoVXNlcnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHsgdXNlcnM6IHVzZXJEYXRhLCB0b3RhbCB9ID0gYXdhaXQgQWRtaW5TZXJ2aWNlLmdldEFsbFVzZXJzKFxuICAgICAgICBjdXJyZW50UGFnZSxcbiAgICAgICAgdXNlcnNQZXJQYWdlLFxuICAgICAgICBzZWFyY2hUZXJtLnRyaW0oKSB8fCB1bmRlZmluZWQsXG4gICAgICAgIHNlbGVjdGVkUm9sZSAhPT0gJ2FsbCcgPyBzZWxlY3RlZFJvbGUgOiB1bmRlZmluZWRcbiAgICAgIClcbiAgICAgIHNldFVzZXJzKHVzZXJEYXRhKVxuICAgICAgc2V0VG90YWxVc2Vycyh0b3RhbClcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgdXNlcnMnKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJvbGVVcGRhdGUgPSBhc3luYyAodXNlcklkOiBzdHJpbmcsIG5ld1JvbGU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBBZG1pblNlcnZpY2UudXBkYXRlVXNlclJvbGUodXNlcklkLCBuZXdSb2xlKVxuICAgICAgYXdhaXQgZmV0Y2hVc2VycygpIC8vIFJlZnJlc2ggdGhlIGxpc3RcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gdXBkYXRlIHVzZXIgcm9sZScpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQmFuVXNlciA9IGFzeW5jICh1c2VySWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VyID0gdXNlcnMuZmluZCh1ID0+IHUuaWQgPT09IHVzZXJJZClcbiAgICAgIGNvbnN0IGJhblVudGlsID0gdXNlcj8uYmFubmVkX3VudGlsID8gdW5kZWZpbmVkIDogbmV3IERhdGUoRGF0ZS5ub3coKSArIDMwICogMjQgKiA2MCAqIDYwICogMTAwMClcbiAgICAgIGF3YWl0IEFkbWluU2VydmljZS5iYW5Vc2VyKHVzZXJJZCwgYmFuVW50aWwpXG4gICAgICBhd2FpdCBmZXRjaFVzZXJzKCkgLy8gUmVmcmVzaCB0aGUgbGlzdFxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgYXdhaXQgc2hvd0FsZXJ0KHtcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6ICdGYWlsZWQgdG8gYmFuL3VuYmFuIHVzZXInLFxuICAgICAgICB2YXJpYW50OiAnZGFuZ2VyJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVWaWV3VXNlckRhdGEgPSAodXNlcjogQWRtaW5Vc2VyKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRVc2VyRGF0YSh1c2VyKVxuICAgIHNldFNob3dVc2VyRGF0YU1vZGFsKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVWaWV3UmVmZXJyYWxUcmVlID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSB1c2Vycy5maW5kKHUgPT4gdS5pZCA9PT0gdXNlcklkKVxuICAgICAgaWYgKCF1c2VyKSByZXR1cm5cblxuICAgICAgc2V0U2VsZWN0ZWRVc2VySW5mbyh1c2VyKVxuICAgICAgc2V0U2hvd1JlZmVycmFsVHJlZSh0cnVlKVxuICAgICAgc2V0VHJlZUxvYWRpbmcodHJ1ZSlcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hZG1pbi9uZXR3b3JrLXRyZWU/cm9vdFVzZXJJZD0ke3VzZXJJZH0mbWF4RGVwdGg9MTAmbGF6eT10cnVlYClcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZXJyb3IgfHwgJ0ZhaWxlZCB0byBsb2FkIHJlZmVycmFsIHRyZWUnKVxuICAgICAgfVxuXG4gICAgICBzZXRTZWxlY3RlZFVzZXJUcmVlKHJlc3VsdC5kYXRhKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHJlZmVycmFsIHRyZWU6JywgZXJyb3IpXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIGxvYWQgcmVmZXJyYWwgdHJlZScpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFRyZWVMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFVzZXJzIGFyZSBhbHJlYWR5IGZpbHRlcmVkIHNlcnZlci1zaWRlLCBubyBuZWVkIGZvciBjbGllbnQtc2lkZSBmaWx0ZXJpbmdcbiAgY29uc3QgZmlsdGVyZWRVc2VycyA9IHVzZXJzXG5cbiAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbCh0b3RhbFVzZXJzIC8gdXNlcnNQZXJQYWdlKVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxBZG1pbkxheW91dD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctZ3JheS0zMDAgcm91bmRlZCB3LTEvNCBtYi02XCI+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEwIGJnLWdyYXktMzAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IDEwIH0pLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIGJnLWdyYXktMzAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMzAwIHJvdW5kZWQgdy0xLzQgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLWdyYXktMzAwIHJvdW5kZWQgdy0xLzNcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0FkbWluTGF5b3V0PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEFkbWluTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+VXNlcnMgTWFuYWdlbWVudDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TWFuYWdlIHVzZXIgYWNjb3VudHMgYW5kIHBlcm1pc3Npb25zPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPHNwYW4+e3RvdGFsVXNlcnN9IHRvdGFsIHVzZXJzPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmlsdGVycyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIHVzZXJzIGJ5IG5hbWUgb3IgZW1haWwuLi5cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTEwIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge3NlYXJjaGluZyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5LWJsdWVcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNtOnctNDhcIj5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFJvbGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZFJvbGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktYmx1ZSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPkFsbCBSb2xlczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJ1c2VyXCI+VXNlcnM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWRtaW5cIj5BZG1pbnM8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFVzZXJzIFRhYmxlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFVzZXJcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFJvbGVcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFJlZmVycmFsIFJhbmtcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgV2FsbGV0IEJhbGFuY2VcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIEpvaW5lZFxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgQWN0aW9uc1xuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAge2ZpbHRlcmVkVXNlcnMubWFwKCh1c2VyKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8dHIga2V5PXt1c2VyLmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHJvdW5kZWQtZnVsbCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5hdmF0YXJfdXJsID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3VzZXIuYXZhdGFyX3VybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiUHJvZmlsZSBwaWN0dXJlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgb2JqZWN0LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgYmctcHJpbWFyeS1ibHVlLzEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LWJsdWVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5mdWxsX25hbWU/LmNoYXJBdCgwKSB8fCB1c2VyLmVtYWlsPy5jaGFyQXQoMCkgfHwgJ1UnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFJlZmVycmFsIFJhbmsgQmFkZ2UgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIC1yaWdodC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJlZmVycmFsUmFua0JhZGdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyVHlwZT17dXNlci51c2VyX3R5cGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5mdWxsX25hbWUgfHwgJ05vIG5hbWUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyLmVtYWlsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIucGhvbmUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyLnBob25lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMi41IHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSAnYWRtaW4nIHx8IHVzZXIuaXNfc3VwZXJfYWRtaW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuaXNfc3VwZXJfYWRtaW4gPyAnU3VwZXIgQWRtaW4nIDogdXNlci5yb2xlIHx8ICdVc2VyJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8UmVmZXJyYWxSYW5rQmFkZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJUeXBlPXt1c2VyLnVzZXJfdHlwZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dMYWJlbD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VyLmJhbm5lZF91bnRpbFxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyLmJhbm5lZF91bnRpbCA/ICdCYW5uZWQnIDogJ0FjdGl2ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFJzIHsodXNlci53YWxsZXRfYmFsYW5jZSB8fCAwKS50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAge25ldyBEYXRlKHVzZXIuY3JlYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7IXVzZXIuaXNfc3VwZXJfYWRtaW4gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXJBY3Rpb25zXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHVzZXI9e3VzZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uUm9sZVVwZGF0ZT17aGFuZGxlUm9sZVVwZGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25CYW5Vc2VyPXtoYW5kbGVCYW5Vc2VyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvblZpZXdUcmVlPXtoYW5kbGVWaWV3UmVmZXJyYWxUcmVlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvblZpZXdVc2VyRGF0YT17aGFuZGxlVmlld1VzZXJEYXRhfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQYWdpbmF0aW9uICovfVxuICAgICAgICB7dG90YWxQYWdlcyA+IDEgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICBTaG93aW5nIHsoKGN1cnJlbnRQYWdlIC0gMSkgKiB1c2Vyc1BlclBhZ2UpICsgMX0gdG8ge01hdGgubWluKGN1cnJlbnRQYWdlICogdXNlcnNQZXJQYWdlLCB0b3RhbFVzZXJzKX0gb2Yge3RvdGFsVXNlcnN9IHVzZXJzXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKHByZXYgPT4gTWF0aC5tYXgocHJldiAtIDEsIDEpKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPT09IDF9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBQcmV2aW91c1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKHByZXYgPT4gTWF0aC5taW4ocHJldiArIDEsIHRvdGFsUGFnZXMpKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPT09IHRvdGFsUGFnZXN9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS01MCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBOZXh0XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFJlZmVycmFsIFRyZWUgTW9kYWwgKi99XG4gICAgICAgIHtzaG93UmVmZXJyYWxUcmVlICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgdy1mdWxsIG1heC13LTZ4bCBoLTUvNiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgPE5ldHdvcmsgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlJlZmVycmFsIFRyZWU8L2gzPlxuICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRVc2VySW5mbyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRVc2VySW5mby5mdWxsX25hbWUgfHwgJ04vQSd9ICh7c2VsZWN0ZWRVc2VySW5mby5lbWFpbH0pXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZXRTaG93UmVmZXJyYWxUcmVlKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFVzZXJUcmVlKG51bGwpXG4gICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkVXNlckluZm8obnVsbClcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICA8RW5oYW5jZWRSZWZlcnJhbFRyZWVcbiAgICAgICAgICAgICAgICAgIHJvb3ROb2RlPXtzZWxlY3RlZFVzZXJUcmVlfVxuICAgICAgICAgICAgICAgICAgbG9hZGluZz17dHJlZUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgICBvbk5vZGVDbGljaz17KHVzZXIpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ05vZGUgY2xpY2tlZDonLCB1c2VyKVxuICAgICAgICAgICAgICAgICAgICAvLyBZb3UgY2FuIGFkZCBtb3JlIGZ1bmN0aW9uYWxpdHkgaGVyZSBsaWtlIHNob3dpbmcgdXNlciBkZXRhaWxzXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1mdWxsXCJcbiAgICAgICAgICAgICAgICAgIG9uUmVmcmVzaD17KCkgPT4gaGFuZGxlVmlld1JlZmVycmFsVHJlZShzZWxlY3RlZFVzZXJJbmZvPy5pZCB8fCAnJyl9XG4gICAgICAgICAgICAgICAgICBpc1JlZnJlc2hpbmc9e3RyZWVMb2FkaW5nfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICBDbGljayBvbiBub2RlcyB0byB2aWV3IGRldGFpbHMg4oCiIEV4cGFuZC9jb2xsYXBzZSB1c2luZyB0aGUgYXJyb3cgYnV0dG9uc1xuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93UmVmZXJyYWxUcmVlKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkVXNlclRyZWUobnVsbClcbiAgICAgICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZFVzZXJJbmZvKG51bGwpXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBDbG9zZVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFZpZXcgVXNlciBEYXRhIE1vZGFsICovfVxuICAgICAgICB7c2hvd1VzZXJEYXRhTW9kYWwgJiYgc2VsZWN0ZWRVc2VyRGF0YSAmJiAoXG4gICAgICAgICAgPFZpZXdVc2VyRGF0YU1vZGFsXG4gICAgICAgICAgICBpc09wZW49e3Nob3dVc2VyRGF0YU1vZGFsfVxuICAgICAgICAgICAgb25DbG9zZT17KCkgPT4ge1xuICAgICAgICAgICAgICBzZXRTaG93VXNlckRhdGFNb2RhbChmYWxzZSlcbiAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRVc2VyRGF0YShudWxsKVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIHVzZXI9e3NlbGVjdGVkVXNlckRhdGF9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvQWRtaW5MYXlvdXQ+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIlVzZXJzIiwiU2VhcmNoIiwiTW9yZVZlcnRpY2FsIiwiU2hpZWxkIiwiQmFuIiwiQ2FsZW5kYXIiLCJNYWlsIiwiUGhvbmUiLCJOZXR3b3JrIiwiVXNlciIsIlgiLCJBZG1pbkxheW91dCIsImR5bmFtaWMiLCJFbmhhbmNlZFJlZmVycmFsVHJlZSIsImxvYWRpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJzc3IiLCJSZWZlcnJhbFJhbmtCYWRnZSIsIkFkbWluU2VydmljZSIsIlZpZXdVc2VyRGF0YU1vZGFsIiwiVXNlckFjdGlvbnMiLCJ1c2VyIiwib25Sb2xlVXBkYXRlIiwib25CYW5Vc2VyIiwib25WaWV3VHJlZSIsIm9uVmlld1VzZXJEYXRhIiwic2hvd01lbnUiLCJzZXRTaG93TWVudSIsIm1lbnVSZWYiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJyZWYiLCJidXR0b24iLCJvbkNsaWNrIiwiaWQiLCJyb2xlIiwiYmFubmVkX3VudGlsIiwiQWRtaW5Vc2VycyIsInVzZXJzIiwic2V0VXNlcnMiLCJzZXRMb2FkaW5nIiwic2VhcmNoaW5nIiwic2V0U2VhcmNoaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwiY3VycmVudFBhZ2UiLCJzZXRDdXJyZW50UGFnZSIsInRvdGFsVXNlcnMiLCJzZXRUb3RhbFVzZXJzIiwic2VsZWN0ZWRSb2xlIiwic2V0U2VsZWN0ZWRSb2xlIiwic2hvd1JlZmVycmFsVHJlZSIsInNldFNob3dSZWZlcnJhbFRyZWUiLCJzZWxlY3RlZFVzZXJUcmVlIiwic2V0U2VsZWN0ZWRVc2VyVHJlZSIsInRyZWVMb2FkaW5nIiwic2V0VHJlZUxvYWRpbmciLCJzZWxlY3RlZFVzZXJJbmZvIiwic2V0U2VsZWN0ZWRVc2VySW5mbyIsInNob3dVc2VyRGF0YU1vZGFsIiwic2V0U2hvd1VzZXJEYXRhTW9kYWwiLCJzZWxlY3RlZFVzZXJEYXRhIiwic2V0U2VsZWN0ZWRVc2VyRGF0YSIsInVzZXJzUGVyUGFnZSIsImZldGNoVXNlcnMiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwidXNlckRhdGEiLCJ0b3RhbCIsImdldEFsbFVzZXJzIiwidHJpbSIsInVuZGVmaW5lZCIsImVyciIsIkVycm9yIiwibWVzc2FnZSIsImhhbmRsZVJvbGVVcGRhdGUiLCJ1c2VySWQiLCJuZXdSb2xlIiwidXBkYXRlVXNlclJvbGUiLCJhbGVydCIsImhhbmRsZUJhblVzZXIiLCJmaW5kIiwidSIsImJhblVudGlsIiwiRGF0ZSIsIm5vdyIsImJhblVzZXIiLCJzaG93QWxlcnQiLCJ0aXRsZSIsInZhcmlhbnQiLCJoYW5kbGVWaWV3VXNlckRhdGEiLCJoYW5kbGVWaWV3UmVmZXJyYWxUcmVlIiwicmVzcG9uc2UiLCJmZXRjaCIsInJlc3VsdCIsImpzb24iLCJvayIsImRhdGEiLCJjb25zb2xlIiwiZmlsdGVyZWRVc2VycyIsInRvdGFsUGFnZXMiLCJNYXRoIiwiY2VpbCIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwiaDEiLCJwIiwic3BhbiIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJzZWxlY3QiLCJvcHRpb24iLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwidGQiLCJhdmF0YXJfdXJsIiwiaW1nIiwic3JjIiwiYWx0IiwiZnVsbF9uYW1lIiwiY2hhckF0IiwiZW1haWwiLCJ1c2VyVHlwZSIsInVzZXJfdHlwZSIsInNpemUiLCJwaG9uZSIsImlzX3N1cGVyX2FkbWluIiwic2hvd0xhYmVsIiwid2FsbGV0X2JhbGFuY2UiLCJ0b0xvY2FsZVN0cmluZyIsImNyZWF0ZWRfYXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJtaW4iLCJwcmV2IiwibWF4IiwiZGlzYWJsZWQiLCJoMyIsInJvb3ROb2RlIiwib25Ob2RlQ2xpY2siLCJsb2ciLCJvblJlZnJlc2giLCJpc1JlZnJlc2hpbmciLCJpc09wZW4iLCJvbkNsb3NlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/users/page.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/users/[userId]/wallet/route";
exports.ids = ["app/api/admin/users/[userId]/wallet/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&page=%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&page=%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_users_userId_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/users/[userId]/wallet/route.ts */ \"(rsc)/./src/app/api/admin/users/[userId]/wallet/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/users/[userId]/wallet/route\",\n        pathname: \"/api/admin/users/[userId]/wallet\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/users/[userId]/wallet/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\users\\\\[userId]\\\\wallet\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_users_userId_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/users/[userId]/wallet/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&page=%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/users/[userId]/wallet/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/admin/users/[userId]/wallet/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabaseAdmin = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\nasync function GET(request, { params }) {\n    try {\n        const { userId } = params;\n        // Get user wallet information\n        const { data: wallet, error: walletError } = await supabaseAdmin.from(\"user_wallets\").select(\"balance\").eq(\"user_id\", userId).single();\n        if (walletError && walletError.code !== \"PGRST116\") {\n            throw walletError;\n        }\n        // Get wallet transaction summaries\n        const { data: transactions, error: transactionError } = await supabaseAdmin.from(\"wallet_transactions\").select(\"transaction_type, amount\").eq(\"wallet_id\", wallet?.id || \"\");\n        if (transactionError && transactionError.code !== \"PGRST116\") {\n            console.error(\"Transaction error:\", transactionError);\n        }\n        // Calculate totals\n        let totalDeposits = 0;\n        let totalWithdrawals = 0;\n        let totalCommissions = 0;\n        if (transactions) {\n            transactions.forEach((transaction)=>{\n                switch(transaction.transaction_type){\n                    case \"deposit\":\n                        totalDeposits += Number(transaction.amount);\n                        break;\n                    case \"withdrawal\":\n                        totalWithdrawals += Number(transaction.amount);\n                        break;\n                    case \"commission\":\n                        totalCommissions += Number(transaction.amount);\n                        break;\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            balance: wallet?.balance || 0,\n            total_deposits: totalDeposits,\n            total_withdrawals: totalWithdrawals,\n            total_commissions: totalCommissions\n        });\n    } catch (error) {\n        console.error(\"Error fetching user wallet data:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch wallet data\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/users/[userId]/wallet/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&page=%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2F%5BuserId%5D%2Fwallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
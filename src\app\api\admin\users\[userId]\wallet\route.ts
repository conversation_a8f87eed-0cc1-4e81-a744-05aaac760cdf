import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params

    // Get user wallet information
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('user_wallets')
      .select('balance')
      .eq('user_id', userId)
      .single()

    if (walletError && walletError.code !== 'PGRST116') {
      throw walletError
    }

    // Get wallet transaction summaries
    const { data: transactions, error: transactionError } = await supabaseAdmin
      .from('wallet_transactions')
      .select('transaction_type, amount')
      .eq('wallet_id', wallet?.id || '')

    if (transactionError && transactionError.code !== 'PGRST116') {
      console.error('Transaction error:', transactionError)
    }

    // Calculate totals
    let totalDeposits = 0
    let totalWithdrawals = 0
    let totalCommissions = 0

    if (transactions) {
      transactions.forEach(transaction => {
        switch (transaction.transaction_type) {
          case 'deposit':
            totalDeposits += Number(transaction.amount)
            break
          case 'withdrawal':
            totalWithdrawals += Number(transaction.amount)
            break
          case 'commission':
            totalCommissions += Number(transaction.amount)
            break
        }
      })
    }

    return NextResponse.json({
      balance: wallet?.balance || 0,
      total_deposits: totalDeposits,
      total_withdrawals: totalWithdrawals,
      total_commissions: totalCommissions
    })

  } catch (error) {
    console.error('Error fetching user wallet data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch wallet data' },
      { status: 500 }
    )
  }
}

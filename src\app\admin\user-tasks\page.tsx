'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import But<PERSON> from '@/components/ui/Button'
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Badge from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Plus, Users, Target, Gift } from 'lucide-react'
import { toast } from 'sonner'
import CreateTaskModal from '@/components/admin/CreateTaskModal'

interface UserTask {
  id: string
  title: string
  description?: string
  task_type: 'sales_target' | 'referral_target' | 'custom'
  target_user_types: string[]
  requirements: Record<string, any>
  reward_amount: number
  reward_type: 'normal_present' | 'annual_present' | 'bonus'
  is_active: boolean
  created_at: string
  expires_at?: string
}

export default function UserTasksPage() {
  const [tasks, setTasks] = useState<UserTask[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      const response = await fetch('/api/admin/user-tasks')
      const data = await response.json()

      if (data.success) {
        setTasks(data.data.tasks)
      } else {
        toast.error('Failed to fetch tasks')
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
      toast.error('Failed to fetch tasks')
    } finally {
      setLoading(false)
    }
  }

  const getTaskTypeBadge = (taskType: string) => {
    switch (taskType) {
      case 'sales_target':
        return <Badge variant="default"><Target className="w-3 h-3 mr-1" />Sales Target</Badge>
      case 'referral_target':
        return <Badge variant="secondary"><Users className="w-3 h-3 mr-1" />Referral Target</Badge>
      case 'custom':
        return <Badge variant="outline">Custom</Badge>
      default:
        return <Badge>{taskType}</Badge>
    }
  }

  const getRewardTypeBadge = (rewardType: string) => {
    switch (rewardType) {
      case 'normal_present':
        return <Badge variant="default"><Gift className="w-3 h-3 mr-1" />Normal Present</Badge>
      case 'annual_present':
        return <Badge variant="secondary"><Gift className="w-3 h-3 mr-1" />Annual Present</Badge>
      case 'bonus':
        return <Badge variant="outline"><Gift className="w-3 h-3 mr-1" />Bonus</Badge>
      default:
        return <Badge>{rewardType}</Badge>
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="h-8 w-8 border-4 border-amber-600 border-t-transparent rounded-full mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading tasks...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">User Tasks</h1>
            <p className="text-gray-600 mt-1">
              Create and manage tasks for users with gift rewards
            </p>
          </div>
          <Button
            className="bg-amber-600 hover:bg-amber-700"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Task
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{tasks.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {tasks.filter(t => t.is_active).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Sales Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {tasks.filter(t => t.task_type === 'sales_target').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Referral Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {tasks.filter(t => t.task_type === 'referral_target').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tasks Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Tasks</CardTitle>
            <p className="text-sm text-gray-500">
              Manage user tasks and their configurations
            </p>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Task</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Target Users</TableHead>
                  <TableHead>Reward</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{task.title}</div>
                        {task.description && (
                          <div className="text-sm text-gray-500 mt-1">
                            {task.description.substring(0, 100)}
                            {task.description.length > 100 && '...'}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getTaskTypeBadge(task.task_type)}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {task.target_user_types.map((type) => (
                          <Badge key={type} variant="outline" className="text-xs">
                            {type.toUpperCase()}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">Rs {task.reward_amount.toLocaleString()}</div>
                        {getRewardTypeBadge(task.reward_type)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={task.is_active ? "default" : "secondary"}>
                        {task.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(task.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        <Button
                          variant={task.is_active ? "outline" : "default"}
                          size="sm"
                        >
                          {task.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {tasks.length === 0 && (
              <div className="text-center py-8">
                <Gift className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                <p className="text-gray-500 mb-4">
                  Create your first user task to get started with the gift system.
                </p>
                <Button
                  className="bg-amber-600 hover:bg-amber-700"
                  onClick={() => setShowCreateModal(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create First Task
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create Task Modal */}
        <CreateTaskModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onTaskCreated={fetchTasks}
        />
      </div>
    </AdminLayout>
  )
}
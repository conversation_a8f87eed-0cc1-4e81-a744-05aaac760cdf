"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/create-okdoi-head/route";
exports.ids = ["app/api/admin/create-okdoi-head/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/create-okdoi-head/route.ts */ \"(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/create-okdoi-head/route\",\n        pathname: \"/api/admin/create-okdoi-head\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/create-okdoi-head/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\create-okdoi-head\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/create-okdoi-head/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRmNyZWF0ZS1va2RvaS1oZWFkJTJGcm91dGUmcGFnZT0lMkZhcGklMkZhZG1pbiUyRmNyZWF0ZS1va2RvaS1oZWFkJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYWRtaW4lMkZjcmVhdGUtb2tkb2ktaGVhZCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkcmVhbSU1Q0Rlc2t0b3AlNUNva2RvaSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDZHJlYW0lNUNEZXNrdG9wJTVDb2tkb2kmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ21DO0FBQ2hIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2tkb2ktbWFya2V0cGxhY2UvPzIwMzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcZHJlYW1cXFxcRGVza3RvcFxcXFxva2RvaVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhZG1pblxcXFxjcmVhdGUtb2tkb2ktaGVhZFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWRtaW4vY3JlYXRlLW9rZG9pLWhlYWQvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hZG1pbi9jcmVhdGUtb2tkb2ktaGVhZFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYWRtaW4vY3JlYXRlLW9rZG9pLWhlYWQvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxkcmVhbVxcXFxEZXNrdG9wXFxcXG9rZG9pXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFkbWluXFxcXGNyZWF0ZS1va2RvaS1oZWFkXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9hZG1pbi9jcmVhdGUtb2tkb2ktaGVhZC9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/create-okdoi-head/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/create-okdoi-head/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if OKDOI Head already exists\n        const existingHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        if (existingHead) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"OKDOI Head already exists\",\n                data: existingHead\n            }, {\n                status: 400\n            });\n        }\n        // Parse request body for custom details (optional)\n        const body = await request.json().catch(()=>({}));\n        const { email, fullName, phone, userId } = body;\n        let okdoiHead;\n        if (userId) {\n            // Assign OKDOI Head to existing user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.assignOKDOIHeadToUser(userId);\n        } else {\n            // Create new OKDOI Head user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.createOKDOIHead(email || \"<EMAIL>\", fullName || \"OKDOI Head\", phone);\n        }\n        console.log(`Admin ${user.email} created OKDOI Head: ${okdoiHead.email}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"OKDOI Head created successfully\",\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error creating OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get OKDOI Head user\n        const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error getting OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code, user_type\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            // Calculate total referrals based on user type\n            let totalReferrals = data.total_downline_count || 0;\n            // For regular users, limit to level 10\n            if (data.user_type === \"user\") {\n                totalReferrals = await this.getTotalReferralsWithLevelLimit(userId, 10);\n            }\n            // For ZM, RSM, and OKDOI Head, show unlimited (use total_downline_count)\n            return {\n                directReferrals: data.direct_referrals_count || 0,\n                totalDownline: data.total_downline_count || 0,\n                totalCommissionEarned: parseFloat(data.total_commission_earned) || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: totalReferrals,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get total referrals with level limit for regular users\n   */ static async getTotalReferralsWithLevelLimit(userId, maxLevel) {\n        try {\n            // Get all referrals within the level limit using referral_hierarchy\n            const { count, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"ancestor_id\", userId).lte(\"level_difference\", maxLevel);\n            if (error) {\n                console.error(\"Error counting referrals with level limit:\", error);\n                return 0;\n            }\n            return count || 0;\n        } catch (error) {\n            console.error(\"Error getting total referrals with level limit:\", error);\n            return 0;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            // First get the placement records\n            const { data: placements, error: placementError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"child_id, position, created_at\").eq(\"parent_id\", userId).order(\"position\");\n            if (placementError) {\n                throw new Error(`Failed to get referral placements: ${placementError.message}`);\n            }\n            if (!placements || placements.length === 0) {\n                return [];\n            }\n            // Get the user details for each child\n            const childIds = placements.map((p)=>p.child_id);\n            const { data: users, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").in(\"id\", childIds);\n            if (userError) {\n                throw new Error(`Failed to get referral users: ${userError.message}`);\n            }\n            // Sort users by placement position\n            const sortedUsers = users?.sort((a, b)=>{\n                const positionA = placements.find((p)=>p.child_id === a.id)?.position || 0;\n                const positionB = placements.find((p)=>p.child_id === b.id)?.position || 0;\n                return positionA - positionB;\n            }) || [];\n            return sortedUsers;\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively using referral_placements (tree structure)\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              placement_type,\n              child:child_id(\n                *,\n                active_subscription:user_subscriptions!left(\n                  id,\n                  status,\n                  expires_at,\n                  package:subscription_packages(\n                    name,\n                    price,\n                    currency\n                  )\n                )\n              )\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();